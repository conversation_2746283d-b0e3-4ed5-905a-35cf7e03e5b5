from tkinter import N
from django.test import TestCase, override_settings
from django.core.cache import cache
from candidates.repositories.application_repository import ApplicationRepository
from django.contrib.auth import get_user_model
from candidates.models import Candidate
from candidates.models import Application
from jobs.models import Job
from businesses.models import Company


User = get_user_model()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestApplicationRepository(TestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        self.user = User.objects.create_user(username="testuser", password="password")
        self.repository = ApplicationRepository()
        self.candidate = Candidate.objects.create(user=self.user)
        self.company = Company.objects.create(name="Test Company")
        self.job = Job.objects.create(
            company_name=self.company,
            name="Test Job",
            min_salary=1000,
            max_salary=5000,
            job_description="Test Job Description")
        self.job2 = self.job = Job.objects.create(
            company_name=self.company,
            name="Test Job 2",
            min_salary=2000,
            max_salary=6000,
            job_description="Test Job Description 2")

    def tearDown(self):
        cache.clear()  # Clear cache after each test
        self.application_data = {
            "applicant": self.candidate,
            "job_applied": self.job,
            "status": "Pending",
        }
        self.application_data_2 = {
            "applicant": self.candidate,
            "job_applied": self.job2,
            "status": "Pending",
        }
    
    def test_create_application(self):
        application = self.repository.create_application(self.application_data)
        self.assertIsNotNone(application)
        self.assertEqual(application.data.applicant.id, self.candidate.id)
        self.assertEqual(application.data.job_applied.id, self.job.id)
        self.assertEqual(application.data.status, "Pending")
    
    def test_create_application_fail(self):
        application_data = self.application_data.copy()
        application_data = self.application_data = {
            "wrong field": self.candidate,
            "wrong field": self.job,
            "status": "Pending",
        }
        application = self.repository.create_application(application_data)
        self.assertIsNone(application.data)
    
    def test_get_application(self):
        application = Application.objects.create(**self.application_data)
        response = self.repository.get_application(application.id)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.applicant.id, self.candidate.id)
        self.assertEqual(response.data.job_applied.id, self.job.id)
    
    def test_get_application_fail(self):
        response = self.repository.get_application(999)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_get_all_applications(self):
        application1 = self.repository.create_application(self.application_data)
        application2 = self.repository.create_application(self.application_data_2)
        response = self.repository.get_all_applications(self.user.id)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(len(response.data), 2)
    
    def test_get_all_applications_fail(self):
        response = self.repository.get_all_applications(9999)

        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_update_application(self):
        application = Application.objects.create(**self.application_data)
        updated_data = {"status": "Accepted"}
        response = self.repository.update_application(application.id, updated_data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.status, "Accepted")
    
    def test_update_application_fail(self):
        updated_data = {"status": "Accepted"}
        response = self.repository.update_application(999, updated_data)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_delete_application(self):
        application = Application.objects.create(**self.application_data)
        response = self.repository.delete_application(application.id)
        self.assertTrue(response.success)
        self.assertIsNone(response.data)
    
    def test_delete_application_fail(self):
        response = self.repository.delete_application(999)
        self.assertFalse(response.success)
