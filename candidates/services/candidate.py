from dataclasses import dataclass
from operator import is_
from typing import Dict, Any, List, Union, Optional
from urllib import request
from django.db.models import Model
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from accounts import serializers
from accounts.repositories.user_repository import RepositoryResponse
from base.services.caching import CachingService
from base.services.logging import LoggingService
import candidates
from candidates.models import Candidate
from candidates.repositories.candidate_repository import CandidateRepository
from candidates.views.serializers import CandidateDataSerializer, GetCandidateSerializer
from rest_framework import status
from django.core.paginator import Paginator, EmptyPage, InvalidPage
from django.db.models import Q


logging_service = LoggingService()


@dataclass
class APIResponse:
    success: bool
    message: str
    data: Optional[Union[Candidate, Dict, List, Model]]
    status: Optional[Any]


class CandidateService:
    def __init__(self, repository: CandidateRepository = None):
        self.repository = repository or CandidateRepository()
        self.caching = CachingService()

    def _validate_candidate_data(self, data: Dict[str, Any]):

        required_fields = ["name"]
        for field in required_fields:
            if field not in data or not data[field]:
                raise ValidationError({field: _("This field is required.")})

        if len(data["name"]) > 255:
            raise ValidationError({"name": _("Name cannot exceed 255 characters.")})

    def create_candidate(self, data: Dict[str, Any]) -> RepositoryResponse:
        self._validate_candidate_data(data)

        response = self.repository.create_candidate(data)

        if not response.success:
            return RepositoryResponse(
                success=False,
                data=None,
                message=response.message,
            )

        return RepositoryResponse(
            success=True,
            data=response.data,
            message=response.message,
        )

    def get_candidate(self, user_id: int) -> RepositoryResponse:
        try:
            cache_key = f"candidate_user:{user_id}"
            cached_candidate = self.caching.getCache(cache_key)
            if cached_candidate:
                return RepositoryResponse(
                    success=True,
                    data=cached_candidate,
                    message="Candidate retrieved successfully from cache.",
                )

            response = self.repository.get_candidate_by_user_id(user_id)

            if not response.success:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=response.message,
                )

            # Cache the candidate data
            serialized_candidate = GetCandidateSerializer(response.data).data
            self.caching.setCache(cache_key, serialized_candidate)

            return RepositoryResponse(
                success=True,
                data=response.data,
                message=response.message,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve candidate",
            )

    def update_candidate(self, user_id, data: Dict[str, Any]) -> RepositoryResponse:
        try:
            # current_data = self.get_candidate(user_id)
            # current_data = current_data.data
            # print(current_data)

            # merged_data = {**current_data, **data}

            # self._validate_candidate_data(data)

            response = self.repository.update_candidate(user_id, data)

            if not response.success:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=response.message,
                )

            # Invalidate cache after update
            cache_key_user = f"candidate_user:{user_id}"
            cache_key_id = f"candidate_id:{response.data.id}"
            self.caching.deleteCache(cache_key_user)
            self.caching.deleteCache(cache_key_id)

            # Also invalidate all candidates cache and related caches
            self.caching.deleteCache("candidates")
            # Clear all candidates query caches (they start with "candidates:")
            self._clear_candidates_query_cache()

            serializer = CandidateDataSerializer(response.data).data

            return RepositoryResponse(
                success=True,
                data=serializer,
                message=response.message,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to update candidate",
            )

    def delete_candidate(self, user_id: str) -> RepositoryResponse:
        try:
            # Get candidate ID before deletion for cache invalidation
            candidate_response = self.repository.get_candidate_by_user_id(user_id)
            candidate_id = candidate_response.data.id if candidate_response.success else None

            response = self.repository.delete_candidate(user_id)

            if not response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=response.message,
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Invalidate cache after deletion
            cache_key_user = f"candidate_user:{user_id}"
            if candidate_id:
                cache_key_id = f"candidate_id:{candidate_id}"
                self.caching.deleteCache(cache_key_id)
            self.caching.deleteCache(cache_key_user)

            # Also invalidate all candidates cache and related caches
            self.caching.deleteCache("candidates")
            # Clear all candidates query caches (they start with "candidates:")
            self._clear_candidates_query_cache()

            return APIResponse(
                success=True,
                data=None,
                message=response.message,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to delete candidate",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_all_candidates(self, page=1, page_size=10) -> APIResponse:
        try:
            response = self.repository.get_all_candidates()

            if not response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=response.message,
                    status=status.HTTP_404_NOT_FOUND,
                )
            paginator = Paginator(response.data, page_size)
            try:
                candidates_page = paginator.page(page)
                serializer_data = CandidateDataSerializer(
                    candidates_page, many=True
                ).data
            except (EmptyPage, InvalidPage):
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="invalid page number",
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return APIResponse(
                success=True,
                message="Candidates retrieved successfully",
                data={
                    "candidates": serializer_data,
                    "total_pages": paginator.num_pages,
                    "total_items": paginator.count,
                    "current_page": int(page),
                    "page_size": int(page_size),
                    "has_next": candidates_page.has_next(),
                    "has_previous": candidates_page.has_previous(),
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to retrieve candidates",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_candidate_by_id(self, candidate_id) -> RepositoryResponse:
        try:
            cache_key = f"candidate_id:{candidate_id}"
            cached_candidate = self.caching.getCache(cache_key)
            if cached_candidate:
                return RepositoryResponse(
                    success=True,
                    data=cached_candidate,
                    message="Candidate retrieved successfully from cache.",
                )

            response = self.repository.get_candidate_by_user_id(candidate_id)

            if not response.success:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=response.message,
                )

            # serialize and cache
            serialized_candidate = GetCandidateSerializer(response.data).data
            self.caching.setCache(cache_key, serialized_candidate)

            return RepositoryResponse(
                success=True,
                data=serialized_candidate,
                message=response.message,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve candidate",
            )

    def handle_query(self, query_params=None):
        """
        Handles querying candidates with caching and database.
        Supports search (first_name, last_name, email, bio) and filter (is_employed, is_active, gender).
        """
        try:
            if query_params is None:
                query_params = {}

            search = query_params.get("q")
            is_employed = query_params.get("is_employed")
            is_active = query_params.get("is_active")
            gender = query_params.get("gender")
            sort_by = query_params.get("sort_by", "created_at")
            sort_order = query_params.get("sort_order", "asc")
            page = int(query_params.get("page", 1))
            page_size = int(query_params.get("page_size", 10))

            filters = {}
            if is_employed is not None:
                filters["is_employed"] = is_employed
            if is_active is not None:
                filters["is_active"] = is_active
            if gender is not None:
                filters["gender"] = gender
            if search:
                filters["search"] = search

            # Create a cache key based on filters, page, page_size, sort_by and sort_order
            filter_str = "_".join([f"{k}:{v}" for k, v in filters.items()])
            cache_key = f"candidates:{filter_str}:{page}:{page_size}:{sort_by}:{sort_order}"

            # Try to get from cache first
            cached_data = self.caching.getCache(cache_key)
            if cached_data:
                return APIResponse(
                    success=True,
                    message="Candidates retrieved successfully from cache",
                    data=cached_data,
                    status=status.HTTP_200_OK,
                )

            # Check if cache has candidates
            has_results, candidates = self._handle_query_with_cache(query_params=filters)
            if not has_results:
                logging_service.log_info("No candidates found in cache, querying database.")
                has_results, candidates = self._handle_query_without_cache(
                    query_params=filters
                )

            # Sort
            reverse = sort_order.lower() == "desc"
            candidates.sort(key=lambda x: x.get(sort_by, ""), reverse=reverse)

            # Paginate
            paginator = Paginator(candidates, page_size)
            try:
                candidates_page = paginator.page(page)
            except (EmptyPage, InvalidPage):
                return APIResponse(
                    success=False,
                    data=None,
                    message="invalid page number",
                    status=status.HTTP_400_BAD_REQUEST,
                )

            data = {
                "candidates": list(candidates_page),
                "total_pages": paginator.num_pages,
                "total_items": paginator.count,
                "current_page": page,
                "page_size": page_size,
                "has_next": candidates_page.has_next(),
                "has_previous": candidates_page.has_previous(),
            }

            # Cache the result
            self.caching.setCache(cache_key, data)

            return APIResponse(
                success=True,
                message="Candidates retrieved successfully",
                data=data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message=f"Error retrieving candidates: {str(e)}",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _handle_query_without_cache(self, query_params=None):
        """
        Handles querying candidates without caching.
        Supports search (first_name, last_name, email, bio) and filter (is_employed, is_active, gender).
        """

        # Query DB with filters/search
        queryset = Candidate.objects.all()
        if "is_employed" in query_params and query_params["is_employed"] is not None:
            queryset = queryset.filter(is_employed=query_params.get("is_employed"))
        if "is_active" in query_params and query_params["is_active"] is not None:
            queryset = queryset.filter(is_active=query_params.get("is_active"))
        if "gender" in query_params:
            queryset = queryset.filter(gender=query_params.get("gender"))

        search = query_params.get("q", "")
        if search:
            queryset = queryset.filter(
                Q(user__first_name__icontains=search)
                | Q(user__last_name__icontains=search)
                | Q(user__email__icontains=search)
                | Q(bio__icontains=search)
            )
        serializer = CandidateDataSerializer(queryset, many=True)
        queryset = serializer.data
        # Add each candidate to the cache list to preserve incremental caching
        for candidate in queryset:
            self.caching.addToCacheList("candidates", candidate)
        return True, queryset

    def _clear_candidates_query_cache(self):
        """
        Helper method to clear all candidates query caches.
        This should be called whenever a candidate is created, updated, or deleted.
        """
        try:
            # In a real Redis implementation, we would use a pattern to delete all keys
            # For now, we'll just clear the entire cache since we're using in-memory cache for tests
            self.caching.clearCache()
            logging_service.log_info("Cleared all candidates query caches")
        except Exception as e:
            logging_service.log_error(f"Error clearing candidates query cache: {e}")

    def _handle_query_with_cache(self, query_params=None):
        """
        Handles querying candidates with caching.
        Supports search (first_name, last_name, email, bio) and filter (is_employed, is_active)
        """
        # get all candidates from cache
        candidates = self.caching.getCache("candidates", [])
        if not candidates:
            return (False, [])
        # Filter candidates based on query parameters

        if "is_employed" in query_params and query_params["is_employed"] is not None:
            candidates = [
                c
                for c in candidates
                if c.get("is_employed") == query_params["is_employed"]
            ]
        if "is_active" in query_params and query_params["is_active"] is not None:
            candidates = [
                c for c in candidates if c.get("is_active") == query_params["is_active"]
            ]
        if gender := query_params.get("gender"):
            candidates = [c for c in candidates if c.get("gender") == gender]
        if search := query_params.get("search"):
            search = search.lower()
            candidates = [
                c
                for c in candidates
                if search in (c.get("user", {}).get("first_name") or "").lower()
                or search in (c.get("user", {}).get("last_name") or "").lower()
                or search in (c.get("user", {}).get("email") or "").lower()
                or search in (c.get("bio") or "").lower()
            ]
        if len(candidates) == 0:
            return False, []
        return True, candidates
