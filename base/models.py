from os import times
import uuid
from django.db import models
from django.contrib.auth.models import User
from django.utils.text import slugify
from time import time


# base model
class BaseModel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    slug = models.SlugField(unique=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="%(class)s_created_by"
    )

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        """Check if this is an update and name hasn't changed"""
        if self.pk and self.slug:
            try:
                old_instance = self.__class__.objects.get(pk=self.pk)
                if old_instance.name == self.name:
                    """Name hasn't changed, keep existing slug"""
                    super().save(*args, **kwargs)
                    return
            except self.__class__.DoesNotExist:
                pass

        """Generate slug (for new instances or when name changed)"""
        if not self.slug or self.pk:
            base_slug = slugify(self.name)
            timestamp = int(time())
            counter = 1
            slug = f"{base_slug}-{timestamp}-{counter}"
            
            """ Check for slug uniqueness within the same model type, not across all BaseModel instances"""
            while self.__class__.objects.filter(slug=slug).exclude(pk=self.pk).exists():
                counter += 1
                slug = f"{base_slug}-{timestamp}-{counter}"
            print(f"Generated slug: {slug} for {self.name} and {self.slug}")
            self.slug = slug
    
        super().save(*args, **kwargs)


class Category(BaseModel):
    class Meta:
        verbose_name_plural = "Skills Categories"


class Skill(BaseModel):
    description = models.CharField(max_length=500, null=True)
    category_name = models.ForeignKey(
        Category, on_delete=models.CASCADE, blank=True, null=True
    )


class Country(BaseModel):
    country_code = models.CharField(max_length=4, unique=True, null=True)

    class Meta:
        verbose_name_plural = "Countries"

    def __str__(self):
        return self.name


class State(BaseModel):
    country_name = models.ForeignKey(Country, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.name}, {self.country_name}"


class City(BaseModel):
    state_name = models.ForeignKey(
        State, on_delete=models.CASCADE, blank=True, null=True
    )

    class Meta:
        verbose_name_plural = "Cities"

    def __str__(self):
        return f"{self.name}, {self.state_name}"


class Address(BaseModel):
    street_address = models.CharField(max_length=255)
    city_name = models.ForeignKey(City, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.street_address}, {self.city_name}"


class SocialMediaPlatforms(BaseModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    handle = models.CharField(max_length=255)
    link = models.URLField(max_length=255)

    def __str__(self):
        return f"{self.user}, {self.handle}"


class UserLanguage(BaseModel):
    pass


class PackageService(BaseModel):
    description = models.TextField(max_length=500, blank=True)
    is_active = models.BooleanField(default=True)


class Package(BaseModel):
    price = models.DecimalField(max_digits=10, decimal_places=2)
    description = models.TextField(max_length=1000)
    pac_services = models.ManyToManyField(
        PackageService,
        blank=True,
        related_name="service_packages",
    )
    is_active = models.BooleanField(default=False)
    has_limit = models.BooleanField(default=False)
    limit = models.PositiveIntegerField(default=0, blank=True, null=True)
    current_job_postings = models.PositiveIntegerField(default=0, blank=True, null=True)
