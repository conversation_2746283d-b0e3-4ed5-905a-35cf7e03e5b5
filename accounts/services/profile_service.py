from requests import delete
from base.email_template.complete_profile import send_profile_completion_email
from rest_framework import status
from datetime import datetime
from functools import partial
from shutil import copy
from zoneinfo import ZoneInfo
from typing import Dict, List, Any
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.db import transaction
from accounts.repositories.user_repository import RepositoryResponse
from base.services.address import AddressService
from base.services.logging import LoggingService
from base.utils.convert_date_string import convert_date_string
from base.utils.status_checker import StatusChecker
from candidates.models import AcademicEducation, Candidate, WorkExperience
from candidates.repositories.candidate_repository import CandidateRepository
from candidates.services import education
from candidates.services.education import AcademicEducationService
from candidates.services.work_experienc import WorkExperienceService
from base.services.skill import SkillService
from base.services.language import LanguageService
from base.services.social_media import SocialMediaPlatformService
from base.models import Category, SocialMediaPlatforms, UserLanguage
from base.services.category import CategoryService
from base.repositories.category import CategoryRepository
from typing import Union, Optional, Dict, List
from dataclasses import dataclass
from django.db.models import Model
import copy
from dotenv import load_dotenv
import os

load_dotenv()

from candidates.views.serializers import (
    AcademicEducationSerializer,
    CandidateSerializer,
    GetCandidateSerializer,
    LanguageSerializer,
    SocialMediaPlatformsSerializer,
    WorkExperienceSerializer,
)

logging_service = LoggingService()


@dataclass
class APIResponse:
    success: bool
    message: str
    data: Optional[Union[Candidate, Dict, List, Model]] = None
    status: Optional[Any] = status.HTTP_400_BAD_REQUEST


class ProfileService:
    def __init__(self):
        self.skill_service = SkillService()
        self.education_service = AcademicEducationService()
        self.work_experience_service = WorkExperienceService()
        self.language = LanguageService()
        self.social_media_service = SocialMediaPlatformService()
        self.candidate_repository = CandidateRepository()
        self.category_repository = CategoryRepository()
        self.category_service = CategoryService()
        self.address_service = AddressService()

    def update_profile(self, user, profile_data: Dict[str, Any]) -> APIResponse:
        updates = []
        candidate = Candidate.objects.filter(user=user).first()
        if not candidate:
            return APIResponse(
                success=False,
                data=None,
                message="Candidate not found for this user",
                status=status.HTTP_404_NOT_FOUND,
            )

        if "date_of_birth" in profile_data:
            date_response = convert_date_string(
                profile_data["date_of_birth"], "date_of_birth"
            )
            if not date_response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=date_response.message,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            profile_data["date_of_birth"] = date_response.data
        if "started_working" in profile_data:
            date_response = convert_date_string(
                profile_data["started_working"], "started_working"
            )
            if not date_response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=date_response.message,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            profile_data["started_working"] = date_response.data

        if "skills" in profile_data:
            updates.append(
                (
                    "Skills",
                    lambda: self.update_skills(candidate, profile_data["skills"]),
                )
            )

        if "education_list" in profile_data:
            updates.append(
                (
                    "Education",
                    lambda: self._update_education(
                        candidate, profile_data["education_list"]
                    ),
                )
            )

        if "work_experiences" in profile_data:
            updates.append(
                (
                    "WorkExperience",
                    lambda: self._update_work_experiences(
                        candidate, profile_data["work_experiences"]
                    ),
                )
            )

        if "languages" in profile_data:
            updates.append(
                (
                    "Languages",
                    lambda: self._update_languages(
                        candidate, profile_data["languages"]
                    ),
                )
            )

        if "social_platforms" in profile_data:
            updates.append(
                (
                    "SocialMediaPlatforms",
                    lambda: self._update_social_media(
                        candidate, profile_data["social_platforms"]
                    ),
                )
            )

        if "address" in profile_data:
            try:
                self.address_service.add_address_to_candidate(
                    profile_data["address"], candidate
                )

            except Exception as e:
                logging_service.log_error(e)
                return APIResponse(
                    success=False,
                    data=None,
                    message="Failed to add address to candidate",
                    status=status.HTTP_400_BAD_REQUEST,
                )

        for (
            update_name,
            update_func,
        ) in updates:
            try:
                response = update_func()
                if not response.success:
                    return APIResponse(
                        success=False,
                        data=None,
                        message=f"Failed to update {update_name}: {response.message}",
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except Exception as e:
                logging_service.log_error(e)
                return APIResponse(
                    success=False,
                    data=None,
                    message=f"Failed to update {update_name}",
                    status=status.HTTP_400_BAD_REQUEST,
                )
        # copy the original data
        profile_data_copy = copy.deepcopy(profile_data)
        # remove all related fields
        related_fields = [
            "skills",
            "education_list",
            "work_experiences",
            "languages",
            "social_platforms",
            "address",
        ]
        for field in related_fields:
            profile_data_copy.pop(field, None)
        # validate the data against the serializer
        serializers = GetCandidateSerializer(data=profile_data_copy, partial=True)
        if not serializers.is_valid():
            return APIResponse(
                success=False,
                data=None,
                message=str(serializers.errors),
                status=status.HTTP_400_BAD_REQUEST,
            )
        # call update profile repository and update
        response = self.candidate_repository.update_candidate(
            candidate.id, profile_data_copy
        )
        if not response.success:
            return APIResponse(
                success=False,
                data=None,
                message=response.message,
                status=status.HTTP_400_BAD_REQUEST,
            )
        return APIResponse(
            success=True,
            data=GetCandidateSerializer(response.data).data,
            message=response.message,
            status=status.HTTP_200_OK,
        )

    def _update_skills(self, candidate, skills):
        skill_ids = []

        for skill_data in skills:
            skill_name = skill_data.get("name")
            category_name = skill_data.get("category_name")

            existing_skill = self.skill_service.get_skill_by_name(skill_name)

            if existing_skill.success:
                skill_ids.append(existing_skill.data.id)
                continue

            category = self._get_or_create_category(category_name)
            if not category.success:

                return APIResponse(
                    success=True,
                    data=None,
                    message="Error getting or creating category",
                )
            new_skill = self._create_skill(skill_name, category)
            if not new_skill.success:

                return APIResponse(
                    success=True,
                    data=None,
                    message="error creating skill '{skill_name}'",
                )
            skill_ids.append(new_skill.id)

        candidate.skills.set(skill_ids)
        candidate.save()
        return RepositoryResponse(
            success=True,
            message="Skills updated successfully",
            data=candidate,
        )

    def _get_or_create_category(self, category_name):
        category_response = self.category_repository.get_category_by_name(
            name=category_name
        )

        if category_response.success:
            return category_response.data

        category = self.category_service.create_category({"name": category_name})
        if not category:
            return RepositoryResponse(
                success=False,
                message=f"Failed to create category '{category_name}': {category_response.message}",
                data=None,
            )

        return RepositoryResponse(
            success=True,
            message=f"Created category '{category_name}'",
            data=category,
        )

    def _create_skill(self, skill_name, category):
        response = self.skill_service.create_skill(
            {"name": skill_name, "category_name": category}
        )

        if not response.success:
            return RepositoryResponse(
                success=False,
                message=f"Failed to create skill '{skill_name}' in category '{category.name}': {response.message}",
                data=None,
            )

        return RepositoryResponse(
            success=True,
            message=f"Created skill '{skill_name}' in category '{category.name}'",
            data=response.data,
        )

    def update_skills(self, candidate, skills):
        """
        Update candidate's education list.
        code flow:
        1. check if a skills exists. if not, create new one and add its id to the list of skills ids
        2. update candidate's education list with the new list of skills ids
        """

        try:
            candidate.skills.clear()
            for skill in skills:
                category_name = skill.get("category_name", "Uncategorized")

                skills_response = self.skill_service.get_or_create_skill(
                    name=skill.get("name"), category_name=category_name
                )

                if not skills_response.success:
                    return skills_response

                if not candidate.skills.filter(id=skills_response.data.id).exists():
                    candidate.skills.add(skills_response.data.id)

            return APIResponse(
                success=True,
                data=candidate.skills.all(),
                message="Skills updated successfully",
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to update skills",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def _update_education(self, candidate, education_list):
        try:
            # Create a list to store education IDs
            education_ids = []

            # Process each education record individually
            for education_data in education_list:
                serializer = AcademicEducationSerializer(
                    data=education_data, partial=True
                )
                if not serializer.is_valid():
                    for e in serializer.errors:
                        logging_service.log_error(e)
                    return APIResponse(
                        success=False,
                        data=None,
                        message=f"Invalid data for education",
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                education = AcademicEducation.objects.filter(
                    name=serializer.validated_data["school_name"],
                    school_name=serializer.validated_data["school_name"],
                    degree_attained=serializer.validated_data["degree_attained"],
                    started_year=serializer.validated_data["started_year"],
                    ended_year=serializer.validated_data["ended_year"],
                ).first()
                if education:
                    education_ids.append(education.id)
                else:
                    new_education = AcademicEducation.objects.create(
                        **serializer.validated_data,
                    )
                    education_ids.append(new_education.id)

            # Update the candidate's education records
            candidate.academic_education.set(education_ids)
            candidate.save()

            return RepositoryResponse(
                success=True, data=candidate, message="Education updated successfully"
            )

        except Exception as e:
            # Log the specific error for debugging
            logging_service.log_error(e)

            return RepositoryResponse(
                success=False, data=None, message=f"Error updating education"
            )

    def _update_work_experiences(
        self, candidate: Candidate, work_experiences: List[Dict[str, Any]]
    ) -> RepositoryResponse:
        try:
            experience_ids = []

            for experience in work_experiences:
                serializer = WorkExperienceSerializer(data=experience, partial=True)
                if not serializer.is_valid():
                    for e in serializer.errors:
                        logging_service.log_error(e)
                    return APIResponse(
                        success=False,
                        data=None,
                        message=f"Invalid data for work experience",
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                experience = WorkExperience.objects.filter(
                    employment_title=serializer.validated_data["employment_title"],
                    company_name=serializer.validated_data["company_name"],
                    started_from=serializer.validated_data["started_from"],
                    ended_at=serializer.validated_data["ended_at"],
                ).first()
                if experience:
                    experience_ids.append(experience.id)
                else:
                    new_experience = WorkExperience.objects.create(
                        **serializer.validated_data
                    )
                    experience_ids.append(new_experience.id)
            candidate.work_experience.set(experience_ids)
            candidate.save()
            return RepositoryResponse(
                success=True,
                data=candidate,
                message="Work experiences updated successfully",
            )

        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Error updating work experiences",
            )

    def _update_languages(
        self, candidate: Candidate, languages: List[Dict[str, Any]]
    ) -> RepositoryResponse:
        try:
            language_ids = []
            for language in languages:
                serializer = LanguageSerializer(data=language, partial=True)
                if not serializer.is_valid():
                    for e in serializer.errors:
                        logging_service.log_error(e)
                    return APIResponse(
                        success=False,
                        data=None,
                        message=f"Invalid data for language",
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                language = UserLanguage.objects.filter(
                    name=serializer.validated_data["name"]
                ).first()
                if language:
                    language_ids.append(language.id)
                else:
                    new_language = UserLanguage.objects.create(
                        **serializer.validated_data
                    )
                    language_ids.append(new_language.id)

            candidate.languages.set(language_ids)
            candidate.save()

            return RepositoryResponse(
                success=True, data=candidate, message="Languages updated successfully"
            )

        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False, data=None, message=f"Error updating languages"
            )

    def _update_social_media(
        self, candidate: Candidate, social_media_platforms: List[Dict[str, Any]]
    ) -> RepositoryResponse:
        try:
            platform_ids = []
            for platform in social_media_platforms:
                serializer = SocialMediaPlatformsSerializer(data=platform, partial=True)
                if not serializer.is_valid():
                    for e in serializer.errors:
                        logging_service.log_error(e)
                    return APIResponse(
                        success=False,
                        data=None,
                        message=f"Invalid data for social media platform",
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                platform = SocialMediaPlatforms.objects.filter(
                    name=platform["name"],
                    handle=platform["handle"],
                    link=platform["link"],
                ).first()
                if platform:
                    platform_ids.append(platform.id)
                else:
                    new_platform = SocialMediaPlatforms.objects.create(
                        **serializer.validated_data
                    )
                    platform_ids.append(new_platform.id)
            candidate.social_platforms.set(platform_ids)
            candidate.save()
            return RepositoryResponse(
                success=True,
                data=candidate,
                message="Social media platforms updated successfully",
            )

        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Error updating social media",
            )

    def get_profile_status(self, user):
        """
        This method returns a status for a candidate profile
        A complete profile should have all fields filled
        """
        fields = {
            1: ["skills"],
            2: ["academic_education"],
            3: ["work_experience"],
            4: ["location"],
            5: ["phone_number", "date_of_birth", "gender"],
        }
        try:
            candidate = Candidate.objects.get(user=user)
            profile_status = StatusChecker.get_status(
                candidate.id,
                self.candidate_repository.get_candidate,
                fields,
                "candidate",
            )
            # print(type(profile_status))
            # print(profile_status)
            return APIResponse(
                success=profile_status.success,
                message=profile_status.message,
                data=profile_status.data,
                status=profile_status.status,
            )
        except Candidate.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Candidate does not exist",
                status=status.HTTP_404_NOT_FOUND,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to retrieve candidate profile status",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def complete_profile(self, candidate_ids):
        try:
            for id in candidate_ids:
                candidate = Candidate.objects.get(id=id)

                link = f"{os.getenv('ORIGIN_FRONTEND')}/candidate/{id}/profile"
                send_profile_completion_email(
                    candidate.user.email, candidate.user.first_name, link
                )
            return APIResponse(
                success=True,
                data=None,
                message="Profile completion email sent successfully",
                status=status.HTTP_200_OK,
            )

        except Candidate.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Candidate does not exist",
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to retrieve candidate",
                status=status.HTTP_400_BAD_REQUEST,
            )
