import factory
from django.utils import timezone
from datetime import timedelta
import uuid
from django.template.defaultfilters import slugify
from base.models import Package, PackageService
from businesses.models import Company
from .models import Subscription, Invoice
from django.contrib.auth.models import User
from base.factory import UserFactory


class PackageServiceFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = PackageService

    id = factory.LazyFunction(uuid.uuid4)
    name = factory.Faker("word")
    slug = factory.LazyAttribute(lambda o: slugify(o.name))
    date_created = factory.LazyFunction(timezone.now)
    date_updated = factory.LazyFunction(timezone.now)
    created_by = factory.SubFactory(UserFactory)
    description = factory.Faker("paragraph", nb_sentences=3)
    is_active = True


class PackageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Package

    id = factory.LazyFunction(uuid.uuid4)
    name = factory.Sequence(lambda n: f"Package {n}")
    slug = factory.LazyAttribute(lambda o: slugify(o.name))
    date_created = factory.LazyFunction(timezone.now)
    date_updated = factory.LazyFunction(timezone.now)
    created_by = factory.SubFactory(UserFactory)
    price = factory.Faker("pydecimal", left_digits=3, right_digits=2, positive=True)
    description = factory.Faker("paragraph", nb_sentences=5)
    is_active = False

    @factory.post_generation
    def pac_services(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for service in extracted:
                self.pac_services.add(service)
        else:
            self.pac_services.add(PackageServiceFactory())


class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User

    username = factory.Faker("user_name")
    email = factory.Faker("email")
    password = factory.PostGenerationMethodCall("set_password", "password123")


class CompanyFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Company

    name = factory.Sequence(lambda n: f"Company {n}")
    is_active = False
    is_verified = False


class SubscriptionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Subscription

    name = factory.Sequence(lambda n: f"Subscription {n}")
    # slug = factory.LazyAttribute(lambda obj: slugify(obj.name))
    date_created = factory.LazyFunction(timezone.now)
    date_updated = factory.LazyFunction(timezone.now)
    created_by = factory.SubFactory(UserFactory)
    user = factory.SubFactory(UserFactory)
    sub_company = factory.SubFactory(CompanyFactory)
    sub_package = factory.SubFactory(PackageFactory)
    start_date = factory.LazyFunction(timezone.now)
    end_date = factory.LazyFunction(lambda: timezone.now() + timedelta(days=30))
    is_active = True


class InvoiceFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Invoice

    id = factory.LazyFunction(uuid.uuid4)
    name = factory.Sequence(lambda n: f"Invoice {n}")
    slug = factory.Sequence(lambda n: f"invoice-{n}")
    date_created = factory.LazyFunction(timezone.now)
    date_updated = factory.LazyFunction(timezone.now)
    created_by = factory.SubFactory(UserFactory)
    inv_subscription = factory.SubFactory(SubscriptionFactory)
    invoice_number = factory.Sequence(
        lambda n: f"INV-{timezone.now().strftime('%Y%m%d')}-{n:03d}"
    )
    subtotal = factory.Faker(
        "pydecimal", left_digits=3, right_digits=2, positive=True
    )
    total_amount = factory.Faker(
        "pydecimal", left_digits=3, right_digits=2, positive=True
    )
    date_paid = None
    status = "pending"
    payment_method = factory.Faker(
        "random_element", elements=("Credit Card", "PayPal", "Bank Transfer")
    )
