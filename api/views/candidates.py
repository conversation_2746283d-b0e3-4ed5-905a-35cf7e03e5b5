from math import perm
from accounts.services.auth import PermissionDecorators, PermissionValidators
from accounts.views.services import SkillSerializer, verify_user
from base.services.logging import LoggingService
from candidates.models import AcademicEducation, Candidate, WorkExperience
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from base.models import SocialMediaPlatforms, UserLanguage
from candidates.views.serializers import (
    AcademicEducationSerializer,
    CandidateSerializer,
    SocialMediaPlatformsSerializer,
    WorkExperienceSerializer,
    CandidateDataSerializer,
)
from accounts.services.profile_service import ProfileService
from candidates.services.candidate import CandidateService
from django.core.paginator import Paginator, EmptyPage

service = ProfileService()
logging_service = LoggingService()
candidate_services = CandidateService()


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(Candidate, fallback_to_user=True)
def update_profile(request):
    """
    Update candidate profile with mixed update data.
    """
    try:
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )

        result = service.update_profile(user=user, profile_data=request.data)
        if not result.success:
            return Response(
                {"error": result.message}, status=status.HTTP_400_BAD_REQUEST
            )

        # Invalidate profile-related caches
        from base.services.caching import CachingService
        caching = CachingService()
        caching.deleteCache(f"profile_data_user:{user.id}")

        # Also clear candidate profile caches if candidate exists
        try:
            from candidates.models import Candidate
            candidate = Candidate.objects.get(user=user)
            caching.deleteCache(f"candidate_profile:{candidate.id}")
        except Candidate.DoesNotExist:
            pass

        return Response(result.data, status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "Failed to update candidate profile"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(Candidate, fallback_to_user=True)
def get_profile_status(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )
    response = service.get_profile_status(user)
    if not response.success:
        return Response({"error": response.message}, status=response.status)
    return Response(response.data, status=response.status)


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(Candidate, fallback_to_user=True)
@PermissionValidators.can_delete_profile(Candidate)
def delete_candidate(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_403_FORBIDDEN,
        )
    # Get candidate ID before deletion for cache invalidation
    try:
        from candidates.models import Candidate
        candidate = Candidate.objects.get(user=user)
        candidate_id = candidate.id
    except Candidate.DoesNotExist:
        candidate_id = None

    response = candidate_services.delete_candidate(user.id)
    if not response.success:
        return Response({"error": response.message}, status=response.status)

    # Invalidate profile-related caches
    from base.services.caching import CachingService
    caching = CachingService()
    caching.deleteCache(f"profile_data_user:{user.id}")

    # Also clear candidate profile caches if candidate ID was found
    if candidate_id:
        caching.deleteCache(f"candidate_profile:{candidate_id}")

    return Response(response.message, status=response.status)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_all_candidates(request):
    try:
        from base.services.caching import CachingService

        caching = CachingService()
        params = request.query_params

        # Create a cache key based on query parameters
        param_str = "_".join([f"{k}:{v}" for k, v in params.items()])
        cache_key = f"api_all_candidates:{param_str}"

        # Try to get from cache first
        cached_data = caching.getCache(cache_key)
        if cached_data:
            return Response(cached_data, status=status.HTTP_200_OK)

        # If not in cache, get from service
        response = candidate_services.handle_query(params)
        if not response.success:
            return Response({"error": response.message}, status=response.status)

        # Cache the result
        caching.setCache(cache_key, response.data)

        return Response(response.data, status=response.status)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "Failed to retrieve all candidates"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_candidate_bio(request):
    if request.method == "POST":
        # authorization
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        bio = request.data.get("bio")
        if not bio:
            return Response(
                {"message", "Bio is required"}, status=status.HTTP_400_BAD_REQUEST
            )
        try:
            candidate = Candidate.objects.get(user=user)
            candidate.bio = bio
            candidate.save()
            return Response({"Biography is updated"}, status=status.HTTP_200_OK)
        except Candidate.DoesNotExist:
            return Response(
                {"message": "Candidate account found"},
                status=status.HTTP_400_BAD_REQUEST,
            )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_social_links(request):
    if request.method == "POST":
        # authorization
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        handle = request.data.get("handle")
        link = request.data.get("link")
        if not all([handle, link]):
            return Response(
                {"message": "Handle and link are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            social_platform = SocialMediaPlatforms.objects.create(
                handle=link["handle"], link=link["link"]
            )
            platform = SocialMediaPlatformsSerializer(social_platform)
            candidate = Candidate.objects.get(user=user)
            candidate.save()
            return Response(
                {"message": "Social platforms are updated", "platform": platform.data},
                status=status.HTTP_200_OK,
            )
        except Candidate.DoesNotExist:
            return Response(
                {"message": "Candidate account found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            print(e)
            return Response(
                {"message": "There was a an error "}, status=status.HTTP_400_BAD_REQUEST
            )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_cv(request):
    if request.method == "POST":
        # authorization
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            candidate = Candidate.objects.get(user=user)
            try:
                candidate.resume = request.data["cv_file"]
                candidate.save()
                return Response(
                    {"status": "success", "message": "resume updated"},
                    status=status.HTTP_200_OK,
                )
            except Exception as e:
                print(e)
                return Response(
                    {
                        "status": "failed",
                        "message": f"We could not save your image {e}",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            return Response(
                {"status": "failed", "message": f"We could not save your resume {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_work_experiences(request):
    if request.method == "POST":
        # authorization
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )

        employment_title = request.data.get("employment_title")
        company_name = request.data.get("company_name")
        started_from = request.data.get("started_from")
        ended_at = request.data.get("ended_at")
        if not all([employment_title, company_name, started_from, ended_at]):
            return Response(
                {"message", "All fields are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            exp = WorkExperience.objects.create(
                name=employment_title,
                created_by=user,
                employment_title=employment_title,
                company_name=company_name,
                started_from=started_from,
                ended_at=ended_at,
            )
            experience = WorkExperienceSerializer(exp)
            return Response(
                {"message": "Experiences are updated", "experience": experience.data}
            )
        except Exception as e:
            print(e)
            return Response(
                {"message", "there was an error"}, status=status.HTTP_400_BAD_REQUEST
            )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_education(request):
    if request.method == "POST":
        # authorization
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )

        school_name = request.data.get("school_name")
        degree_attained = request.data.get("degree_attained")
        started_year = request.data.get("started_year")
        ended_year = request.data.get("ended_year")

        if not all([degree_attained, started_year, ended_year]):
            return Response(
                {"message", "All fields are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            academic_education = AcademicEducation.objects.create(
                name=school_name,
                degree_attained=degree_attained,
                started_year=started_year,
                ended_year=ended_year,
            )
            education = AcademicEducationSerializer(academic_education)
            return Response(
                {"message": "Education is updated", "experience": education.data}
            )
        except Exception as e:
            print(e)
            return Response(
                {"message", "there was an error"}, status=status.HTTP_400_BAD_REQUEST
            )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_skills(request):
    if request.method == "POST":
        # authorization
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        name = request.data.get("description")
        category_name = request.data.get("category_name")
        description = request.data.get("description")

        if not all(
            [
                name,
                category_name,
                description,
            ]
        ):
            return Response(
                {"message", "All fields are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        from base.models import Skill, Category as SkillCategory

        try:
            category = SkillCategory.objects.get(name=category_name)
            skill_data = Skill.objects.create(
                name=name, category_name=category, description=description
            )
            skill = SkillSerializer(skill_data)
            return Response({"message": "Skills are updated", "experience": skill.data})
        except SkillCategory.DoesNotExist:
            return Response(
                {"message", f"No category called {category_name} found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            print(e)
            return Response(
                {"message", "there was an error"}, status=status.HTTP_400_BAD_REQUEST
            )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_additional_info(request):
    if request.method == "POST":
        # authorization
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )

        phone_number = request.data.get("phone_number")
        languages = request.data.get("languages")
        if not all([phone_number, languages]):
            return Response(
                {"message", "All fields are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        user_languages = []

        try:
            for language in languages:
                if not UserLanguage.objects.filter(name=language["name"]).exists():
                    user_language = UserLanguage.objects.create(
                        name=language["name"], created_by=user
                    )
                    user_languages.append(user_language)
                else:
                    user_language = UserLanguage.objects.get(name=language["name"])
                    print(user_language)
                    user_languages.append(user_language.id)
            candidate = Candidate.objects.get(user=user)
            candidate.phone_number = phone_number
            candidate.languages.set(user_languages)
            candidate.save()

            candidate_data = CandidateSerializer(candidate)
            return Response(
                {"message": "Skills are updated", "candidate": candidate_data.data}
            )
        except Candidate.DoesNotExist:
            return Response(
                {"message": "No candidate with ID found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            print(e)
            return Response(
                {"message": "there was an error"}, status=status.HTTP_400_BAD_REQUEST
            )
